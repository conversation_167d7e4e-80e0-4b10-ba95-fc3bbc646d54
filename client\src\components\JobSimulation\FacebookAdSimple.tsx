import { useEffect, useRef, useState } from 'react';
import { useSetRecoilState } from 'recoil';
import { AppSimulationModalConfig, AppSimulationScreen } from '~/common';
import {
  Input,
  Label,
  OGDialog,
  OGDialogContent,
  OGDialogFooter,
  OGDialogHeader,
  OGDialogTitle,
  RadioGroup,
  RadioGroupItem,
  Slider,
} from '~/components';
import store from '~/store';
import { cn } from '~/utils';
import {
  facebookAdCalculationConfig,
  getCalculationConfig,
} from './AppSimulation/CalculationConfigs';
import { CalculationEngine, formatRange } from './AppSimulation/CalculationEngine';
import LineChart from './AppSimulation/LineChart';

// Extended types for dynamic modal configuration

// Shared dropdown options for budget selection
const budgetDropdownOptions = [
  {
    label: '$1.00',
    value: 1,
    screenId: '005',
    dataContextId: 'budget',
    dataContext: 'Budget selected: $1.00 per day',
    saveToSelections: true,
  },
  {
    label: '$10.00',
    value: 10,
    screenId: '005_03',
    dataContextId: 'budget',
    dataContext: 'Budget selected: $10.00 per day',
    saveToSelections: true,
  },
];

const screens: AppSimulationScreen[] = [
  {
    id: '001',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/001.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Create Ad',
        x1: 55.4,
        y1: 43.25,
        x2: 59.2,
        y2: 46.2,
        left: 55.4,
        top: 43.25,
        width: 3.8,
        height: 2.75,
        action: { type: 'nextScreen' },
      },
      {
        title: 'Create Ad',
        x1: 95.3,
        y1: 1.3,
        x2: 99.2,
        y2: 4.3,
        left: 95.2,
        top: 1.57,
        width: 4.0,
        height: 2.36,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '002',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/002.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Choose a goal:',
        x1: 62.2,
        y1: 12.3,
        x2: 69.8,
        y2: 30.97,
        left: 62.2,
        top: 12.54,
        width: 7.6,
        height: 18.43,
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '003',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/003.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Create post',
        x1: 46.8,
        y1: 40.5,
        x2: 51.1,
        y2: 43.5,
        left: 46.8,
        top: 40.75,
        width: 4.2,
        height: 2.35,
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '004',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/004.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Add photo',
        x1: 5.64,
        y1: 38.99,
        x2: 12.76,
        y2: 43.06,
        left: 5.64,
        top: 38.99,
        width: 7.12,
        height: 4.07,
        action: { type: 'uploadPhoto' },
      },
      {
        title: "Post's text",
        x1: 5.64,
        y1: 55.86,
        x2: 38.72,
        y2: 72.45,
        left: 5.64,
        top: 55.86,
        width: 33.08,
        height: 16.59,
        action: {
          type: 'inputText',
          inputType: 'textarea',
          dataContextId: 'postText',
          dataContextLabel: "Post's text",
          saveToSelections: true,
        },
      },
      {
        title: 'Publish',
        x1: 33.92,
        y1: 92.39,
        x2: 38.68,
        y2: 96.52,
        left: 33.87,
        top: 92.39,
        width: 4.81,
        height: 4.13,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen' },
      },
      {
        title: 'Button (unnamed)',
        x1: 22.58,
        y1: 92.54,
        x2: 26.99,
        y2: 96.54,
        left: 22.58,
        top: 92.54,
        width: 4.41,
        height: 4.0,
        action: { type: 'nextScreen', screenId: '003' },
      },
    ],
    placeholders: [
      {
        id: 'imageSmall',
        type: 'image',
        dataId: 'postImage',
        x1: 5.57,
        y1: 30.7,
        x2: 8.43,
        y2: 36.31,
        title: 'Image small placeholder',
      },
      {
        id: 'imageLarge',
        type: 'image',
        dataId: 'postImage',
        x1: 55.15,
        y1: 31.6,
        x2: 84.2,
        y2: 88.4,
        title: 'Image large placeholder',
      },
      {
        id: 'text',
        type: 'text',
        dataId: 'postText',
        x1: 56.14,
        y1: 22.02,
        x2: 82.93,
        y2: 29.81,
        title: 'Text placeholder',
      },
    ],
  },
  {
    id: '005',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/005_budget01.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Budget',
        x1: 9.49367088607595,
        y1: 61.54844336709283,
        x2: 37.18354430379747,
        y2: 67.6228152947973,
        width: 27.689873417721518,
        height: 6.074371927704476,
        left: 9.49367088607595,
        top: 61.54844336709283,
        action: {
          type: 'dropdown',
          dropdownOptions: budgetDropdownOptions,
        },
      },
      {
        title: 'Audience',
        x1: 39.200949367088604,
        y1: 20.741124263026954,
        x2: 41.25791139240506,
        y2: 24.47919929546047,
        width: 2.0569620253164587,
        height: 3.738075032433514,
        left: 39.200949367088604,
        top: 20.741124263026954,
        action: {
          type: 'modal',
          modalConfig: {
            title: 'Audience Settings',
            description: 'Configure your audience targeting',
            inputs: [
              {
                id: 'location',
                type: 'multiSelect',
                label: 'Location',
                dataId: 'audience_location',
                options: ['Vietnam', 'HongKong', 'China', 'Australia'],
                required: true,
                minSelections: 1,
                defaultValue: ['Vietnam'],
              },
              {
                id: 'age',
                type: 'range',
                label: 'Age Range',
                dataId: 'audience_age',
                min: 18,
                max: 65,
                defaultValue: [18, 65],
                labels: { min: '18', max: '65+' },
                formatType: 'age-range',
              },
              {
                id: 'gender',
                type: 'radio',
                label: 'Gender',
                dataId: 'audience_gender',
                radioOptions: [
                  { value: 'All', label: 'All' },
                  { value: 'Men', label: 'Men' },
                  { value: 'Women', label: 'Women' },
                ],
                defaultValue: 'All',
              },
            ],
          },
        },
      },
      {
        title: 'Publish Ad',
        x1: 73.41772151898735,
        y1: 94.80173584311599,
        x2: 78.1,
        y2: 99.2,
        width: 4.62816455696202,
        height: 4.283210974663405,
        left: 73.41772151898735,
        top: 94.80173584311599,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: {
          // type: 'triggerMessage',
          // message: "I've published the ad with the following settings:",
          // withData: true,
          type: 'nextScreen',
          screenId: '006',
        },
      },
      {
        title: 'Cancel',
        x1: 68.63132911392405,
        y1: 94.95748778116534,
        x2: 73,
        y2: 99,
        width: 4.2721518987341796,
        height: 3.971704721960606,
        left: 68.63132911392405,
        top: 94.95748778116534,
        action: { type: 'nextScreen', screenId: '004' },
      },
    ],
    placeholders: [
      {
        id: 'audience_location',
        type: 'text',
        dataId: 'audience_location',
        x1: 9.***************,
        y1: 24.86858211133896,
        x2: 38.52848101265822,
        y2: 27.282755569785604,
        title: 'Audience Location',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'audience_age',
        type: 'text',
        dataId: 'audience_age',
        x1: 9.25632911392405,
        y1: 27.750014948839798,
        x2: 13.409810126582278,
        y2: 29.774805591407947,
        title: 'Audience Age',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'audience_gender',
        type: 'text',
        dataId: 'audience_gender',
        x1: 9.***************,
        y1: 30.***************,
        x2: 13.***************,
        y2: 32.**************,
        title: 'Audience Gender',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_accounts',
        type: 'text',
        dataId: 'estimated_accounts',
        x1: 45.**************,
        y1: 11.***************,
        x2: 58.**************,
        y2: 14.**************,
        title: 'Estimated Accounts Reached',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_post_engagement',
        type: 'text',
        dataId: 'estimated_post_engagement',
        x1: 45.***************,
        y1: 20.***************,
        x2: 58.**************,
        y2: 24.***************,
        title: 'Estimated Posts Engagement',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'total_budget',
        type: 'text',
        dataId: 'total_budget',
        x1: 54.**************,
        y1: 40.***************,
        x2: 62.**************,
        y2: 43.**************,
        title: 'Total Budget',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
      {
        id: 'budget_per_day',
        type: 'text',
        x1: 45.***************,
        y1: 42.85806820492525,
        x2: 54.153481012658234,
        y2: 46.59614323735877,
        dataId: 'budget_per_day',
        title: 'Budget Per Day',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_vat',
        type: 'text',
        x1: 54.**************,
        y1: 46.751896363710166,
        x2: 62.65822784810127,
        y2: 49.5554526380353,
        dataId: 'estimated_vat',
        title: 'Estimated VAT',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
      {
        id: 'total_amount',
        type: 'text',
        x1: 54.90506329113924,
        y1: 51.42449015425207,
        x2: 62.69778481012657,
        y2: 54.695305807631396,
        dataId: 'total_amount',
        title: 'Total Amount',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
    ],
  },
  {
    id: '005_03',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/005_budget03.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Budget',
        x1: 9.49367088607595,
        y1: 61.54844336709283,
        x2: 37.18354430379747,
        y2: 67.6228152947973,
        width: 27.689873417721518,
        height: 6.074371927704476,
        left: 9.49367088607595,
        top: 61.54844336709283,
        action: {
          type: 'dropdown',
          dropdownOptions: budgetDropdownOptions,
        },
      },
      {
        title: 'Audience',
        x1: 39.200949367088604,
        y1: 20.741124263026954,
        x2: 41.25791139240506,
        y2: 24.47919929546047,
        width: 2.0569620253164587,
        height: 3.738075032433514,
        left: 39.200949367088604,
        top: 20.741124263026954,
        action: {
          type: 'modal',
          modalConfig: {
            title: 'Audience Settings',
            description: 'Configure your audience targeting',
            inputs: [
              {
                id: 'location',
                type: 'multiSelect',
                label: 'Location',
                dataId: 'audience_location',
                options: ['Vietnam', 'HongKong', 'China', 'Australia'],
                required: true,
                minSelections: 1,
                defaultValue: ['Vietnam'],
              },
              {
                id: 'age',
                type: 'range',
                label: 'Age Range',
                dataId: 'audience_age',
                min: 18,
                max: 65,
                defaultValue: [18, 65],
                labels: { min: '18', max: '65+' },
                formatType: 'age-range',
              },
              {
                id: 'gender',
                type: 'radio',
                label: 'Gender',
                dataId: 'audience_gender',
                radioOptions: [
                  { value: 'All', label: 'All' },
                  { value: 'Men', label: 'Men' },
                  { value: 'Women', label: 'Women' },
                ],
                defaultValue: 'All',
              },
            ],
          },
        },
      },
      {
        title: 'Publish Ad',
        x1: 73.41772151898735,
        y1: 94.80173584311599,
        x2: 78.1,
        y2: 99.2,
        width: 4.62816455696202,
        height: 4.283210974663405,
        left: 73.41772151898735,
        top: 94.80173584311599,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: {
          // type: 'triggerMessage',
          // message: "I've published the ad with the following settings:",
          // withData: true,
          type: 'nextScreen',
          screenId: '006',
        },
      },
      {
        title: 'Cancel',
        x1: 68.63132911392405,
        y1: 94.95748778116534,
        x2: 73,
        y2: 99,
        width: 4.2721518987341796,
        height: 3.971704721960606,
        left: 68.63132911392405,
        top: 94.95748778116534,
        action: { type: 'nextScreen', screenId: '004' },
      },
    ],
    placeholders: [
      {
        id: 'audience_location',
        type: 'text',
        dataId: 'audience_location',
        x1: 9.***************,
        y1: 24.86858211133896,
        x2: 38.52848101265822,
        y2: 27.282755569785604,
        title: 'Audience Location',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'audience_age',
        type: 'text',
        dataId: 'audience_age',
        x1: 9.25632911392405,
        y1: 27.750014948839798,
        x2: 13.409810126582278,
        y2: 29.774805591407947,
        title: 'Audience Age',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'audience_gender',
        type: 'text',
        dataId: 'audience_gender',
        x1: 9.***************,
        y1: 30.***************,
        x2: 13.***************,
        y2: 32.**************,
        title: 'Audience Gender',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_accounts',
        type: 'text',
        dataId: 'estimated_accounts',
        x1: 45.**************,
        y1: 11.***************,
        x2: 58.**************,
        y2: 14.**************,
        title: 'Estimated Accounts Reached',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_post_engagement',
        type: 'text',
        dataId: 'estimated_post_engagement',
        x1: 45.***************,
        y1: 20.***************,
        x2: 58.**************,
        y2: 24.***************,
        title: 'Estimated Posts Engagement',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'total_budget',
        type: 'text',
        dataId: 'total_budget',
        x1: 54.**************,
        y1: 40.***************,
        x2: 62.**************,
        y2: 43.**************,
        title: 'Total Budget',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
      {
        id: 'budget_per_day',
        type: 'text',
        x1: 45.***************,
        y1: 42.85806820492525,
        x2: 54.153481012658234,
        y2: 46.59614323735877,
        dataId: 'budget_per_day',
        title: 'Budget Per Day',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
        },
      },
      {
        id: 'estimated_vat',
        type: 'text',
        x1: 54.**************,
        y1: 46.751896363710166,
        x2: 62.65822784810127,
        y2: 49.5554526380353,
        dataId: 'estimated_vat',
        title: 'Estimated VAT',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
      {
        id: 'total_amount',
        type: 'text',
        x1: 54.90506329113924,
        y1: 51.42449015425207,
        x2: 62.69778481012657,
        y2: 54.695305807631396,
        dataId: 'total_amount',
        title: 'Total Amount',
        style: {
          color: '#000',
          backgroundColor: 'transparent',
          justifyContent: 'right',
        },
      },
    ],
  },
  {
    id: '006',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/006.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'View Post',
        x1: 63.005050505050505,
        y1: 91.15662649319022,
        x2: 96.11742424242425,
        y2: 95.27733913408223,
        top: 91.15662649319022,
        left: 63.005050505050505,
        width: 33.112373737373744,
        height: 4.120712640892009,
        action: { type: 'nextScreen', screenId: '007' },
      },
    ],
    charts: [
      {
        id: 'chart1',
        type: 'line',
        data: {
          datasets: [
            { x: 10.04, y: 67.41, label: '10k', time: 6 },
            { x: 14.38, y: 65.8, label: '15k', time: 9 },
            { x: 19.18, y: 63.73, label: '50k', time: 12 },
            { x: 24.12, y: 61.89, label: '60k', time: 15 },
            { x: 28.31, y: 60.97, label: '70k', time: 24 },
            { x: 35.8, y: 60.28, label: '75k', time: 48 },
            { x: 37.3, y: 54.99, label: '300k', time: 60 },
            { x: 46.44, y: 53.15, label: '400k', time: 90 },
            { x: 55.88, y: 52.0, label: '500k', time: 120 },
          ],
          chartArea: {
            left: 3.89,
            top: 43.25,
            width: 55.73,
            height: 29.22,
          },
        },
      },
    ],
    placeholders: [
      {
        id: 'imageSmall',
        type: 'image',
        dataId: 'postImage',
        x1: 6.05,
        y1: 1.71,
        x2: 9.87,
        y2: 7.33,
        title: 'Image small placeholder',
      },
      {
        id: 'imageLarge',
        type: 'image',
        dataId: 'postImage',
        x1: 63.9,
        y1: 35.77,
        x2: 95.34,
        y2: 77.8,
        title: 'Image large placeholder',
      },
      {
        id: 'postText1',
        type: 'text',
        dataId: 'postText',
        x1: 10.35,
        y1: 1.96,
        x2: 96.14,
        y2: 5.87,
        title: 'Post text placeholder',
        style: {
          whiteSpace: 'unset',
          backgroundColor: 'unset',
        },
      },
      {
        id: 'postText2',
        type: 'text',
        dataId: 'postText',
        x1: 64.94,
        y1: 23.71,
        x2: 93.59,
        y2: 31.54,
        title: 'Post text placeholder',
      },
      {
        id: 'view1',
        type: 'text',
        x1: 3.93,
        y1: 18.33,
        x2: 9.17,
        y2: 22.58,
        title: 'Post views placeholder',
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          fontWeight: 'bold',
          border: '0px',
        },
        dataByTime: [
          { time: 0, value: '0' },
          { time: 6, value: '10K' },
          { time: 9, value: '15K' },
          { time: 12, value: '50K' },
          { time: 15, value: '60K' },
          { time: 24, value: '70K' },
          { time: 48, value: '75K' },
          { time: 60, value: '300K' },
          { time: 90, value: '400K' },
          { time: 120, value: '500K' },
        ],
      },
      {
        id: 'view2',
        type: 'text',
        x1: 3.78,
        y1: 31.52,
        x2: 9.17,
        y2: 36.66,
        title: 'Post views placeholder',
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          fontWeight: 'bold',
          border: '0px',
        },
        dataByTime: [
          { time: 0, value: '0' },
          { time: 6, value: '10K' },
          { time: 9, value: '15K' },
          { time: 12, value: '50K' },
          { time: 15, value: '60K' },
          { time: 24, value: '70K' },
          { time: 48, value: '75K' },
          { time: 60, value: '300K' },
          { time: 90, value: '400K' },
          { time: 120, value: '500K' },
        ],
      },
      {
        id: 'reactions',
        type: 'text',
        x1: 3.64,
        y1: 81.37,
        x2: 11.21,
        y2: 86.29,
        title: 'Post reactions placeholder',
        dataId: 'postReactions',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          fontWeight: 'bold',
          border: '0px',
        },
      },
      {
        id: 'comments',
        type: 'text',
        x1: 18.63,
        y1: 81.6,
        x2: 26.2,
        y2: 85.62,
        title: 'Post comments placeholder',
        dataId: 'postComments',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          fontWeight: 'bold',
          border: '0px',
        },
      },
      {
        id: 'shares',
        type: 'text',
        x1: 33.77,
        y1: 81.15,
        x2: 40.32,
        y2: 85.85,
        title: 'Post shares placeholder',
        dataId: 'postShares',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          fontWeight: 'bold',
          border: '0px',
        },
      },
    ],
    actions: [
      {
        type: 'triggerMessage',
        message: "I've published the ad with the following settings:",
        withData: true,
      },
    ],
  },
  {
    id: '007',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/newsfeed.png',
    bgColor: 'bg-[#64767e]',
    elements: [
      {
        title: 'Ad',
        x1: 34.02777777777778,
        y1: 5.504984354216049,
        x2: 66.41414141414141,
        y2: 99.3989871598336,
        top: 5.504984354216049,
        left: 34.02777777777778,
        width: 32.38636363636363,
        height: 93.89400280561755,
        action: { type: 'nextScreen', screenId: '006' },
      },
    ],
    placeholders: [
      {
        id: 'imageSmall',
        type: 'image',
        dataId: 'postImage',
        x1: 34.6123417721519,
        y1: 6.666150037010931,
        x2: 36.550632911392405,
        y2: 10.541818663180079,
        title: 'Image small placeholder',
      },
      {
        id: 'imageLarge',
        type: 'image',
        dataId: 'postImage',
        x1: 34.05854430379747,
        y1: 23.331525129538264,
        x2: 66.29746835443038,
        y2: 86.42741036357197,
        title: 'Image large placeholder',
      },
      {
        id: 'companyName',
        type: 'text',
        dataId: 'companyName',
        x1: 36.82753164556962,
        y1: 6.743663409534315,
        x2: 46.75632911392405,
        y2: 8.759011095142272,
        title: 'Company text placeholder',
        initialValue: 'BrightWave Media',
        style: {
          alignItems: 'start',
          whiteSpace: 'unset',
          backgroundColor: 'unset',
          fontWeight: 'bold',
        },
      },
      {
        id: 'postHeadline',
        type: 'text',
        dataId: 'postHeadline',
        x1: 34.05854430379747,
        y1: 86.63411426731459,
        x2: 59.6123417721519,
        y2: 92.21507708899817,
        title: 'Post headline placeholder',
        initialValue: 'BrightWave Media',
        style: {
          alignItems: 'center',
          whiteSpace: 'unset',
          backgroundColor: 'unset',
          fontWeight: 'bold',
        },
      },
      {
        id: 'postText1',
        type: 'text',
        dataId: 'postText',
        x1: 34.651898734177216,
        y1: 11.084412270843758,
        x2: 65.70411392405063,
        y2: 22.55639140430443,
        title: 'Post text placeholder',
        style: {
          alignItems: 'start',
          backgroundColor: 'unset',
        },
      },
      {
        id: 'reactions',
        type: 'text',
        x1: 35.60126582278481,
        y1: 92.5251305790917,
        x2: 40.625,
        y2: 95.31561198993347,
        title: 'Post reactions placeholder',
        dataId: 'postReactions',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          border: '0px',
        },
      },
      {
        id: 'comments',
        type: 'text',
        x1: 53.44145569620253,
        y1: 92.68015732413846,
        x2: 57.83227848101266,
        y2: 95.31561198993347,
        title: 'Post comments placeholder',
        dataId: 'postComments',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          border: '0px',
          justifyContent: 'right',
        },
      },
      {
        id: 'shares',
        type: 'text',
        x1: 61.07594936708861,
        y1: 92.75767069666185,
        x2: 63.88449367088608,
        y2: 95.16058524488672,
        title: 'Post shares placeholder',
        dataId: 'postShares',
        increaseByTime: true,
        increaseFrom: 0,
        style: {
          fontSize: '1rem',
          backgroundColor: 'unset',
          color: '#000',
          border: '0px',
          justifyContent: 'right',
        },
      },
    ],
  },
];

const FacebookAdSimple = () => {
  console.log('::: FacebookAdSimple :::');
  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);

  const imageRef: React.MutableRefObject<HTMLImageElement | null> = useRef(null);
  const containerRef: React.MutableRefObject<HTMLDivElement | null> = useRef(null);
  const imageContainerRef = useRef<HTMLDivElement | null>(null);

  const [currentScreenIndex, setCurrentScreenIndex] = useState(0);
  const [showDropdown, setShowDropdown] = useState(false);
  const [dropdownOptions, setDropdownOptions] = useState<
    Array<{
      label: string;
      screenId: string;
      dataContext?: string;
      dataContextId?: string;
      saveToSelections?: boolean;
    }>
  >([]);
  const [dropdownPosition, setDropdownPosition] = useState({ x: 0, y: 0 });
  const [userSelections, setUserSelections] = useState<Record<string, string>>({});

  // State for data content (images and text) - now stored by data ID instead of screen ID
  const [dataContent, setDataContent] = useState<
    Record<
      string,
      {
        uploadedImage?: string;
        inputText?: string;
      }
    >
  >({});

  const [hoveredElements, setHoveredElements] = useState<number[]>([]);

  // State for text input dialog
  const [showTextDialog, setShowTextDialog] = useState(false);
  const [textInputData, setTextInputData] = useState<{
    type: string;
    value: string;
    dataContextId: string;
    saveToSelections: boolean;
    dataContextLabel: string;
  }>({
    type: 'input',
    value: '',
    dataContextId: '',
    saveToSelections: false,
    dataContextLabel: '',
  });

  // State for dynamic modal
  const [showModal, setShowModal] = useState(false);
  const [currentModalConfig, setCurrentModalConfig] = useState<AppSimulationModalConfig | null>(
    null,
  );
  const [modalValues, setModalValues] = useState<Record<string, any>>({});

  // State for time-based placeholders
  // const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [timeBasedValues, setTimeBasedValues] = useState<Record<string, string>>({});
  const [lastUpdateTimes, setLastUpdateTimes] = useState<Record<string, number>>({});

  // State for dynamic selections (replaces hardcoded states)
  // const [dynamicSelections, setDynamicSelections] = useState<Record<string, any>>({});
  const [dynamicSelections, setDynamicSelections] = useState<Record<string, any>>({
    budget: 1,
    audience_location: ['Vietnam'],
    audience_age: [18, 65],
    audience_gender: 'All',
  });
  const [calculatedEstimates, setCalculatedEstimates] = useState<Record<string, string>>({});

  // Helper function to format value using built-in formatters
  const formatValueWithType = (value: any, formatType?: string, formatConfig?: any): string => {
    if (!formatType) {
      if (Array.isArray(value) && value.length >= 2) {
        return `${value[0]} - ${value[1]}`;
      }
      return String(value);
    }

    switch (formatType) {
      case 'age-range':
        if (Array.isArray(value) && value.length >= 2) {
          return formatRange([value[0], value[1]], {
            maxValue: formatConfig?.maxValue || 65,
            maxLabel: formatConfig?.maxLabel || '65+',
            separator: formatConfig?.separator,
          });
        }
        break;
      case 'range':
        if (Array.isArray(value) && value.length >= 2) {
          return formatRange([value[0], value[1]], formatConfig);
        }
        break;
      case 'currency':
        if (typeof value === 'number') {
          return `$${value.toFixed(2)} USD`;
        }
        break;
      case 'percentage':
        if (typeof value === 'number') {
          return `${value}%`;
        }
        break;
      default:
        if (Array.isArray(value) && value.length >= 2) {
          return `${value[0]} - ${value[1]}`;
        }
        return String(value);
    }

    return String(value);
  };

  // Initialize Calculation Engine dynamically
  const [calculationEngine] = useState(() => {
    // TODO: using props 'facebook-ad'
    const config = getCalculationConfig('facebook-ad') || facebookAdCalculationConfig;
    return new CalculationEngine(config);
  });

  // const calculationEngine = new CalculationEngine(
  //   getCalculationConfig('facebook-ad') || facebookAdCalculationConfig,
  // );

  // Helper function to check if a screen has time-based placeholders
  const hasTimeBasedPlaceholders = (screen: AppSimulationScreen): boolean => {
    return (
      screen.placeholders?.some(
        (placeholder) => placeholder.dataByTime || placeholder.increaseByTime,
      ) || false
    );
  };

  // Calculate estimates whenever parameters change using Calculation Engine
  useEffect(() => {
    if (!dynamicSelections || Object.keys(dynamicSelections).length === 0) return;
    calculationEngine.updateValues(dynamicSelections);

    const estimates = calculationEngine.getAllValues();
    setCalculatedEstimates(estimates);

    console.log('Calculation Engine - Updated values ::: ', dynamicSelections);
    console.log('Calculation Engine - Results:', estimates);
  }, [dynamicSelections, calculationEngine]);

  // Reset input values and errors when screen changes
  useEffect(() => {
    if (screens[currentScreenIndex].actions?.length) {
      for (const action of screens[currentScreenIndex].actions) {
        if (action.type === 'triggerMessage') {
          let finalMessage = action.message || '';

          if (action.withData && Object.keys(userSelections).length > 0) {
            const selectionsText = Object.entries(userSelections)
              .map(([key, value]) => `${key}: ${value}`)
              .join(', ');
            finalMessage = `${finalMessage} ${selectionsText}`;
            setJobsimulationTriggerMessage({
              message: finalMessage,
              isTriggered: true,
            });
          }

          break;
        }
      }
    }

    setShowDropdown(false);

    // Initialize timer for screens with time-based placeholders
    if (hasTimeBasedPlaceholders(screens[currentScreenIndex])) {
      setStartTime(Date.now());
      // setElapsedTime(0);
      setLastUpdateTimes({});
      // Initialize time-based values
      const initialValues: Record<string, string> = {};
      screens[currentScreenIndex].placeholders?.forEach((placeholder) => {
        if (placeholder.dataByTime) {
          // Find initial value (time = 0)
          const initialData = placeholder.dataByTime.find((data) => data.time === 0);
          if (placeholder.dataId && dataContent[placeholder.dataId]?.inputText) {
            initialValues[placeholder.id] = dataContent[placeholder.dataId]?.inputText!;
          } else if (initialData) {
            initialValues[placeholder.id] = initialData.value;
          }
        } else if (placeholder.increaseByTime && placeholder.increaseFrom !== undefined) {
          if (placeholder.dataId && dataContent[placeholder.dataId]?.inputText) {
            initialValues[placeholder.id] = dataContent[placeholder.dataId]?.inputText!;
          } else {
            initialValues[placeholder.id] = placeholder.increaseFrom.toString();
          }
        }
      });
      setTimeBasedValues(initialValues);
    }
  }, [currentScreenIndex]);

  // Timer for time-based placeholders
  useEffect(() => {
    if (startTime === null || !hasTimeBasedPlaceholders(screens[currentScreenIndex])) return;

    const interval = setInterval(() => {
      const currentTime = Date.now();
      const elapsed = Math.floor((currentTime - startTime) / 1000); // Convert to seconds
      // setElapsedTime(elapsed);

      // Update time-based values
      const updatedValues: Record<string, string> = {};
      screens[currentScreenIndex].placeholders?.forEach((placeholder) => {
        if (placeholder.dataByTime) {
          // Find the appropriate value for current elapsed time
          let currentValue = '0';
          for (let i = placeholder.dataByTime.length - 1; i >= 0; i--) {
            if (elapsed >= placeholder.dataByTime[i].time) {
              currentValue = placeholder.dataByTime[i].value;
              break;
            }
          }
          updatedValues[placeholder.id] = currentValue;
        } else if (placeholder.increaseByTime && placeholder.increaseFrom !== undefined) {
          // Update every 10 seconds with random increase
          const currentVal = parseInt(
            timeBasedValues[placeholder.id] || placeholder.increaseFrom.toString(),
          );
          const lastUpdate = lastUpdateTimes[placeholder.id] || 0;
          const currentInterval = Math.floor(elapsed / 10);
          const lastInterval = Math.floor(lastUpdate / 10);

          if (elapsed >= 10 && currentInterval > lastInterval) {
            // Time for an update - add random increase
            const randomIncrease = Math.floor(Math.random() * 10) + 1;
            const newValue = currentVal + randomIncrease;
            updatedValues[placeholder.id] = newValue.toString();
            if (placeholder.dataId) {
              setDataContent((prev) => ({
                ...prev,
                [placeholder.dataId!]: {
                  ...prev[placeholder.dataId!],
                  inputText: newValue.toString(),
                },
              }));
            }
            console.log(
              `Updating ${placeholder.id}: ${currentVal} + ${randomIncrease} = ${newValue} at ${elapsed}s`,
            );
            // Update last update time
            setLastUpdateTimes((prev) => ({
              ...prev,
              [placeholder.id]: elapsed,
            }));
          } else {
            // Keep current value
            updatedValues[placeholder.id] = currentVal.toString();
            if (placeholder.dataId) {
              setDataContent((prev) => ({
                ...prev,
                [placeholder.dataId!]: {
                  ...prev[placeholder.dataId!],
                  inputText: currentVal.toString(),
                },
              }));
            }
          }
        }
      });

      setTimeBasedValues((prev) => ({ ...prev, ...updatedValues }));
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, [startTime, currentScreenIndex, timeBasedValues]);

  // Function to find screen index by ID
  const findScreenIndexById = (screenId: string) => {
    return screens.findIndex((screen) => screen.id === screenId);
  };

  // Handle photo upload
  const handlePhotoUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;
      const reader = new FileReader();
      reader.onload = (event) => {
        const imageUrl = event.target?.result as string;
        // Crop image to 240x240
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          canvas.width = 240;
          canvas.height = 240;

          if (!ctx) return;
          // Calculate crop dimensions to maintain aspect ratio
          const size = Math.min(img.width, img.height);
          const x = (img.width - size) / 2;
          const y = (img.height - size) / 2;

          ctx.drawImage(img, x, y, size, size, 0, 0, 240, 240);
          const croppedImageUrl = canvas.toDataURL();

          // Save image data by dataId instead of screenId
          setDataContent((prev) => ({
            ...prev,
            postImage: {
              ...prev.postImage,
              uploadedImage: croppedImageUrl,
            },
          }));

          // Note: Photo upload doesn't save to userSelections as per requirement
        };
        img.src = imageUrl;
      };
      reader.readAsDataURL(file);
    };
    input.click();
  };

  // Handle text input
  const handleTextInput = (
    inputType: 'input' | 'textarea' = 'input',
    label?: string,
    dataContextId?: string,
    saveToSelections?: boolean,
  ) => {
    // Get current text value from dataContent using dataContextId
    setTextInputData({
      type: inputType,
      value: dataContent[dataContextId || 'unknown']?.inputText || '',
      dataContextId: dataContextId || '',
      saveToSelections: saveToSelections || false,
      dataContextLabel: label || 'Text Content',
    });
    setShowTextDialog(true);
  };

  // Handle text dialog submit
  const handleTextDialogSubmit = () => {
    if (textInputData?.saveToSelections && textInputData.dataContextId) {
      setDataContent((prev) => ({
        ...prev,
        [textInputData.dataContextId || 'unknown']: {
          ...prev[textInputData.dataContextId || 'unknown'],
          inputText: textInputData.value,
        },
      }));

      setUserSelections((prev) => ({
        ...prev,
        [textInputData.dataContextId || 'unknown']:
          `${textInputData.dataContextLabel || 'Text'}: "${textInputData.value}"`,
      }));
    }

    setShowTextDialog(false);
    setTextInputData({
      type: 'input',
      value: '',
      dataContextId: '',
      saveToSelections: false,
      dataContextLabel: '',
    });
  };

  // Handle dynamic modal submit
  const handleModalSubmit = () => {
    if (!currentModalConfig) return;

    console.log('Modal submit - current values:', modalValues);

    const updatedDataContent: Record<string, any> = {};
    const updatedUserSelections: Record<string, string> = {};

    currentModalConfig.inputs.forEach((input) => {
      const value = modalValues[input.id];
      if (value !== undefined && input.dataId) {
        let formattedValue = '';

        switch (input.type) {
          case 'multiSelect':
            formattedValue = Array.isArray(value) ? value.join(', ') : '';
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            setDynamicSelections((prev) => ({
              ...prev,
              [input.dataId]: Array.isArray(value) ? value : [],
            }));
            break;
          case 'range':
            if (Array.isArray(value)) {
              formattedValue = formatValueWithType(value, input.formatType, input.formatConfig);
            }
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            if (Array.isArray(value)) {
              setDynamicSelections((prev) => ({
                ...prev,
                [input.dataId]: [value[0], value[1]],
              }));
            }
            break;
          case 'radio':
          case 'text':
          case 'textarea':
            formattedValue = String(value);
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            setDynamicSelections((prev) => ({
              ...prev,
              [input.dataId]: String(value),
            }));
            break;
          default:
            formattedValue = String(value);
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
        }

        updatedDataContent[input.dataId] = {
          inputText: formattedValue,
        };
      }
    });

    // Update placeholders with selected data
    setDataContent((prev) => ({
      ...prev,
      ...updatedDataContent,
    }));

    // Update user selections
    setUserSelections((prev) => ({
      ...prev,
      ...updatedUserSelections,
    }));

    setShowModal(false);
    setCurrentModalConfig(null);
    setModalValues({});
  };

  useEffect(() => {
    function handleResize() {
      if (imageRef.current && containerRef.current) {
        // const naturalWidth = imageRef.current.naturalWidth;
        // const naturalHeight = imageRef.current.naturalHeight;

        // const containerWidth = containerRef.current.clientWidth ?? 0;
        const containerHeight = containerRef.current.clientHeight ?? 0;

        // const scale = Math.min(containerWidth / naturalWidth, containerHeight / naturalHeight);

        // const scaledWidth = naturalWidth * scale;
        // const scaledHeight = naturalHeight * scale;

        imageRef.current.style.width = `auto`;
        imageRef.current.style.maxHeight = `${containerHeight}px`;

        if (imageContainerRef.current) {
          imageContainerRef.current.style.width = `auto`;
          imageContainerRef.current.style.maxHeight = `${containerHeight}px`;
        }
      }
    }

    handleResize();
  }, [imageRef.current?.src, containerRef?.current?.clientWidth, imageContainerRef]);

  const handleClickButton = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!imageRef.current) return;

    const screenElements = screens[currentScreenIndex]?.elements || [];
    const indexes = screenElements.map((_, index) => index);
    setHoveredElements(indexes);
  };

  const handleDropdownOptionClick = (
    screenId: string,
    optionLabel: string,
    dataContext?: string,
    dataContextId?: string,
    saveToSelections?: boolean,
    value?: number,
  ) => {
    const targetIndex = findScreenIndexById(screenId);
    if (targetIndex !== -1) {
      setCurrentScreenIndex(targetIndex);
    }

    // Save user selection if saveToSelections is true
    if (saveToSelections && dataContextId) {
      setUserSelections((prev) => ({
        ...prev,
        [dataContextId]: dataContext || optionLabel,
      }));
    }

    // Update dynamic selections for calculation
    if (dataContextId && value !== undefined) {
      setDynamicSelections((prev) => ({
        ...prev,
        [dataContextId]: value,
      }));
    }

    setShowDropdown(false);
  };

  useEffect(() => {
    if (imageRef.current) {
      imageRef.current.src = screens[currentScreenIndex].image;
    }
  }, [imageRef, currentScreenIndex]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showDropdown) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showDropdown]);

  // Calculate positions for buttons and clickable inputs
  const getElementStyles = (button: any): React.CSSProperties => {
    return {
      position: 'absolute' as const,
      left: `${button.x1}%`,
      top: `${button.y1}%`,
      width: `${button.x2 - button.x1}%`,
      height: `${button.y2 - button.y1}%`,
      cursor: 'pointer',
      zIndex: 10,
      backgroundColor: hoveredElements.includes(button.index)
        ? (button.backgroundColor ?? 'rgba(0, 123, 255, 0.2)')
        : 'transparent',
      border: hoveredElements.includes(button.index)
        ? (button.border ?? '2px solid rgba(0, 123, 255, 0.5)')
        : 'none',
      borderRadius: '4px',
      transition: 'all 0.2s ease',
      pointerEvents: 'auto',
    };
  };

  return (
    <>
      <div
        className={cn(
          'flex h-full flex-col items-center justify-center',
          screens[currentScreenIndex].bgColor ? `${screens[currentScreenIndex].bgColor}` : '',
        )}
        ref={containerRef}
      >
        <div className="relative" ref={imageContainerRef}>
          <img
            ref={imageRef}
            onClick={handleClickButton}
            // className="max-h-full w-auto"
            style={{ display: 'block' }} // Ensure image is block to avoid layout issues
          />

          {/* Overlay for buttons */}
          {screens[currentScreenIndex].elements?.map((button, index) => (
            <div
              key={`button-${screens[currentScreenIndex].id}-${index}`}
              style={getElementStyles({ ...button, index })}
              onMouseEnter={() => setHoveredElements([index])}
              onMouseLeave={() => setHoveredElements([])}
              onClick={(e) => {
                e.stopPropagation();
                if (button.action.type === 'nextScreen') {
                  if (button.action.screenId) {
                    const targetIndex = findScreenIndexById(button.action.screenId);
                    if (targetIndex !== -1) {
                      setCurrentScreenIndex(targetIndex);
                    }
                  } else {
                    setCurrentScreenIndex((prevIndex) => prevIndex + 1);
                  }
                } else if (button.action.type === 'dropdown' && button.action.dropdownOptions) {
                  // Show dropdown
                  setDropdownOptions(button.action.dropdownOptions);
                  setDropdownPosition({
                    x: (button.x1 + button.x2) / 2,
                    y: button.y2 + 2,
                  });
                  setShowDropdown(true);
                } else if (button.action.type === 'uploadPhoto') {
                  // Handle photo upload
                  handlePhotoUpload();
                } else if (button.action.type === 'inputText') {
                  // Handle text input
                  handleTextInput(
                    button.action.inputType,
                    button.action.dataContextLabel,
                    button.action.dataContextId,
                    button.action.saveToSelections,
                  );
                } else if (button.action.type === 'modal') {
                  // Handle dynamic modal
                  if (button.action.modalConfig) {
                    setCurrentModalConfig(button.action.modalConfig);
                    // Initialize modal values with current selections or defaults
                    const initialValues: Record<string, any> = {};
                    button.action.modalConfig.inputs.forEach((input) => {
                      // Generic initialization based on dataId
                      const currentValue = dynamicSelections[input.dataId!];
                      if (currentValue !== undefined) {
                        initialValues[input.id] = currentValue;
                      } else if (input.defaultValue !== undefined) {
                        initialValues[input.id] = input.defaultValue;
                      } else if (input.type === 'multiSelect') {
                        initialValues[input.id] = [];
                      } else if (
                        input.type === 'range' &&
                        input.min !== undefined &&
                        input.max !== undefined
                      ) {
                        initialValues[input.id] = [input.min, input.max];
                      } else {
                        initialValues[input.id] = '';
                      }
                    });
                    console.log('Modal initialized with values:', initialValues);
                    setModalValues(initialValues);
                    setShowModal(true);
                  }
                } else if (button.action.type === 'triggerMessage') {
                  // Handle trigger message
                  let finalMessage = button.action.message || '';

                  if (button.action.withData && Object.keys(userSelections).length > 0) {
                    const selectionsText = Object.entries(userSelections)
                      .map(([key, value]) => `${key}: ${value}`)
                      .join(', ');
                    finalMessage = `${finalMessage} ${selectionsText}`;
                  }

                  console.log('finalMessage ::: 2 ', finalMessage);

                  setJobsimulationTriggerMessage({
                    message: finalMessage,
                    isTriggered: true,
                  });
                }
              }}
              title={button.title}
            />
          ))}

          {/* Placeholders overlay */}
          {screens[currentScreenIndex].placeholders?.map((placeholder, index) => {
            // Get data content by dataId instead of screenId
            const currentContent = placeholder.dataId ? dataContent[placeholder.dataId] : undefined;

            // Check if this placeholder has time-based data
            const hasTimeBasedData = placeholder.dataByTime || placeholder.increaseByTime;
            const timeBasedValue = hasTimeBasedData
              ? (dataContent[placeholder.dataId || '']?.inputText ??
                timeBasedValues[placeholder.id])
              : undefined;

            if (placeholder.type === 'image' && currentContent?.uploadedImage) {
              return (
                <div
                  key={`placeholder-${placeholder.id || index}`}
                  style={{
                    position: 'absolute',
                    left: `${placeholder.x1}%`,
                    top: `${placeholder.y1}%`,
                    width: `${placeholder.x2 - placeholder.x1}%`,
                    height: `${placeholder.y2 - placeholder.y1}%`,
                    zIndex: 15,
                    overflow: 'hidden',
                    borderRadius: '4px',
                  }}
                >
                  <img
                    src={currentContent.uploadedImage}
                    alt="Uploaded content"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  />
                </div>
              );
            }

            if (placeholder.type === 'text') {
              // Determine what text to display
              let displayText = '';
              if (hasTimeBasedData && timeBasedValue) {
                displayText = timeBasedValue;
              } else if (placeholder.dataId && calculatedEstimates[placeholder.dataId]) {
                // Use calculated estimates for specific dataIds
                displayText = calculatedEstimates[placeholder.dataId];
              } else if (currentContent?.inputText) {
                displayText = currentContent.inputText;
              } else if (currentContent?.inputText === undefined && placeholder.initialValue) {
                displayText = placeholder.initialValue;
              }

              if (displayText) {
                return (
                  <div
                    key={`placeholder-${placeholder.id || index}`}
                    style={{
                      position: 'absolute',
                      left: `${placeholder.x1}%`,
                      top: `${placeholder.y1}%`,
                      width: `${placeholder.x2 - placeholder.x1}%`,
                      height: `${placeholder.y2 - placeholder.y1}%`,
                      zIndex: 15,
                      display: 'flex',
                      justifyContent: 'left',
                      alignItems: 'center',
                      backgroundColor: hasTimeBasedData
                        ? 'rgba(59, 130, 246, 0.1)'
                        : 'rgba(255, 255, 255, 0.9)',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                      lineHeight: '0.75rem',
                      padding: '4px 2px 2px 2px',
                      color: hasTimeBasedData ? '#1e40af' : '#333',
                      textAlign: 'left',
                      overflow: 'auto',
                      wordBreak: 'break-word',
                      whiteSpace: 'pre-line',
                      fontWeight: hasTimeBasedData ? 'bold' : 'normal',
                      border: hasTimeBasedData ? '1px solid rgba(59, 130, 246, 0.3)' : 'none',
                      ...placeholder.style,
                    }}
                  >
                    {displayText}
                  </div>
                );
              }
            }

            return null;
          })}

          {/* Charts overlay */}
          {screens[currentScreenIndex].charts?.map((chart, index) => {
            if (chart.type === 'line') {
              return <LineChart key={`chart-${chart.id || index}`} data={chart.data} />;
            }
            return null;
          })}

          {/* Dropdown overlay */}
          {showDropdown && (
            <div
              style={{
                position: 'absolute',
                left: `${dropdownPosition.x}%`,
                top: `${dropdownPosition.y}%`,
                transform: 'translateX(-50%)',
                zIndex: 20,
                backgroundColor: 'white',
                border: '1px solid #ccc',
                borderRadius: '4px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                minWidth: '150px',
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {dropdownOptions.map((option, index) => (
                <div
                  key={index}
                  style={{
                    padding: '8px 12px',
                    cursor: 'pointer',
                    borderBottom: index < dropdownOptions.length - 1 ? '1px solid #eee' : 'none',
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f5f5f5';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                  }}
                  onClick={() => {
                    handleDropdownOptionClick(
                      option.screenId,
                      option.label,
                      option.dataContext,
                      option.dataContextId,
                      option.saveToSelections,
                      (option as any).value,
                    );
                  }}
                >
                  {option.label}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Text Input Dialog */}
      <OGDialog open={showTextDialog} onOpenChange={setShowTextDialog}>
        <OGDialogContent
          title="Enter Text"
          className="max-w-[600px] bg-background text-text-primary shadow-2xl"
        >
          <OGDialogHeader>
            <OGDialogTitle>
              Enter your {textInputData?.dataContextLabel || 'text content'}
            </OGDialogTitle>
          </OGDialogHeader>

          <div className="flex flex-col gap-4 py-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="textInput">{textInputData?.dataContextLabel || 'Text Content'}</Label>
              {textInputData?.type === 'textarea' ? (
                <textarea
                  id="textInput"
                  value={textInputData.value}
                  onChange={(e) => {
                    setTextInputData((prev) => ({ ...prev, value: e.target.value }));
                  }}
                  className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-all duration-200 placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Enter your text here..."
                  rows={5}
                />
              ) : (
                <Input
                  id="textInput"
                  value={textInputData.value}
                  onChange={(e) => {
                    setTextInputData((prev) => ({ ...prev, value: e.target.value }));
                  }}
                  className="transition-all duration-200"
                  placeholder="Enter your text here..."
                />
              )}
            </div>
          </div>

          <OGDialogFooter>
            <div className="flex gap-2">
              <button
                onClick={() => setShowTextDialog(false)}
                className="mt-3 block w-fit rounded bg-gray-100 px-4 py-2 text-base font-semibold text-neutral-900 hover:opacity-90"
              >
                Cancel
              </button>
              <button
                onClick={handleTextDialogSubmit}
                className="mt-3 block w-fit rounded bg-neutral-900 px-4 py-2 text-base font-semibold text-white hover:opacity-90"
              >
                OK
              </button>
            </div>
          </OGDialogFooter>
        </OGDialogContent>
      </OGDialog>

      {/* Dynamic Modal */}
      <OGDialog open={showModal} onOpenChange={setShowModal}>
        <OGDialogContent
          title={currentModalConfig?.title || 'Modal'}
          className="max-w-[600px] bg-background text-text-primary shadow-2xl"
        >
          <OGDialogHeader>
            <OGDialogTitle>
              {currentModalConfig?.description || currentModalConfig?.title}
            </OGDialogTitle>
          </OGDialogHeader>

          <div className="flex flex-col gap-6 py-4">
            {currentModalConfig?.inputs.map((input) => (
              <div key={input.id} className="flex flex-col gap-2">
                <Label htmlFor={input.id}>{input.label}</Label>

                {/* Multi-Select */}
                {input.type === 'multiSelect' && input.options && (
                  <div className="space-y-2">
                    {input.options.map((option) => (
                      <label key={option} className="flex cursor-pointer items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={modalValues[input.id]?.includes(option) || false}
                          onChange={(e) => {
                            const currentValues = modalValues[input.id] || [];
                            if (e.target.checked) {
                              setModalValues((prev) => ({
                                ...prev,
                                [input.id]: [...currentValues, option],
                              }));
                            } else {
                              setModalValues((prev) => ({
                                ...prev,
                                [input.id]: currentValues.filter((v: string) => v !== option),
                              }));
                            }
                          }}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm">{option}</span>
                      </label>
                    ))}
                  </div>
                )}

                {/* Range Slider */}
                {input.type === 'range' && input.min !== undefined && input.max !== undefined && (
                  <div className="px-2">
                    <Slider
                      value={modalValues[input.id] || [input.min, input.max]}
                      onValueChange={(value) =>
                        setModalValues((prev) => ({
                          ...prev,
                          [input.id]: value,
                        }))
                      }
                      min={input.min}
                      max={input.max}
                      step={input.step || 1}
                      className="w-full"
                    />
                    <div className="mt-1 flex justify-between text-sm text-gray-500">
                      <span>{input.labels?.min || input.min}</span>
                      <span className="font-medium">
                        {formatValueWithType(
                          modalValues[input.id] || [input.min, input.max],
                          input.formatType,
                          input.formatConfig,
                        )}
                      </span>
                      <span>{input.labels?.max || input.max}</span>
                    </div>
                  </div>
                )}

                {/* Radio Group */}
                {input.type === 'radio' && input.radioOptions && (
                  <RadioGroup
                    value={modalValues[input.id] || input.defaultValue || ''}
                    onValueChange={(value) =>
                      setModalValues((prev) => ({
                        ...prev,
                        [input.id]: value,
                      }))
                    }
                  >
                    {input.radioOptions.map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.value} id={`${input.id}-${option.value}`} />
                        <Label htmlFor={`${input.id}-${option.value}`} className="cursor-pointer">
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                )}

                {/* Text Input */}
                {input.type === 'text' && (
                  <Input
                    id={input.id}
                    value={modalValues[input.id] || ''}
                    onChange={(e) =>
                      setModalValues((prev) => ({
                        ...prev,
                        [input.id]: e.target.value,
                      }))
                    }
                    placeholder={input.placeholder}
                    className="w-full"
                  />
                )}

                {/* Textarea */}
                {input.type === 'textarea' && (
                  <textarea
                    id={input.id}
                    value={modalValues[input.id] || ''}
                    onChange={(e) =>
                      setModalValues((prev) => ({
                        ...prev,
                        [input.id]: e.target.value,
                      }))
                    }
                    placeholder={input.placeholder}
                    className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-all duration-200 placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    rows={5}
                  />
                )}
              </div>
            ))}
          </div>

          <OGDialogFooter>
            <div className="flex gap-2">
              <button
                onClick={() => setShowModal(false)}
                className="mt-3 block w-fit rounded bg-gray-100 px-4 py-2 text-base font-semibold text-neutral-900 hover:opacity-90"
              >
                Cancel
              </button>
              <button
                onClick={handleModalSubmit}
                className="mt-3 block w-fit rounded bg-neutral-900 px-4 py-2 text-base font-semibold text-white hover:opacity-90"
              >
                Apply
              </button>
            </div>
          </OGDialogFooter>
        </OGDialogContent>
      </OGDialog>
    </>
  );
};

export default FacebookAdSimple;
