// TODO: fetch data from db

import { CalculationConfig } from './CalculationEngine';

// Facebook Ad Calculation Configuration
export const facebookAdCalculationConfig: CalculationConfig = {
  rules: {
    estimated_accounts: {
      id: 'estimated_accounts',
      type: 'range',
      inputs: ['budget', 'audience_location', 'audience_age', 'audience_gender'],
      baseValue: 500,
      multipliers: {
        budget: { type: 'linear', factor: 1 },
        audience_location: { type: 'array_length', factor: 1.5 },
        audience_age: { type: 'range_width', maxRange: 47, minValue: 0.6 },
        audience_gender: {
          type: 'conditional',
          conditions: [
            { condition: 'value === "All"', value: 1.5 },
            { condition: 'value === "Men"', value: 0.8 },
            { condition: 'value === "Women"', value: 0.7 },
          ],
        },
      },
      formatters: [{ type: 'number' }],
    },
    estimated_post_engagement: {
      id: 'estimated_post_engagement',
      type: 'range',
      inputs: ['budget', 'audience_location', 'audience_age', 'audience_gender'],
      baseValue: 100,
      multipliers: {
        budget: { type: 'linear', factor: 1 },
        audience_location: { type: 'array_length', factor: 1.5 },
        audience_age: { type: 'range_width', maxRange: 47, minValue: 0.6 },
        audience_gender: {
          type: 'conditional',
          conditions: [
            { condition: 'value === "All"', value: 1.5 },
            { condition: 'value === "Men"', value: 0.8 },
            { condition: 'value === "Women"', value: 0.7 },
          ],
        },
      },
      formatters: [{ type: 'number' }],
    },
    total_budget: {
      id: 'total_budget',
      type: 'formula',
      inputs: ['budget'],
      formula: 'budget * 7',
      formatters: [{ type: 'currency' }],
    },
    budget_per_day: {
      id: 'budget_per_day',
      type: 'formula',
      inputs: ['budget'],
      formula: 'budget',
      formatters: [{ type: 'custom', template: '${value}.00 a day x 7 days' }],
    },
    estimated_vat: {
      id: 'estimated_vat',
      type: 'formula',
      inputs: ['total_budget_raw'],
      formula: 'total_budget_raw * 0.05',
      formatters: [{ type: 'currency' }],
    },
    total_budget_raw: {
      id: 'total_budget_raw',
      type: 'formula',
      inputs: ['budget'],
      formula: 'budget * 7',
    },
    total_amount: {
      id: 'total_amount',
      type: 'formula',
      inputs: ['total_budget_raw', 'estimated_vat_raw'],
      formula: 'total_budget_raw + estimated_vat_raw',
      formatters: [{ type: 'currency' }],
    },
    estimated_vat_raw: {
      id: 'estimated_vat_raw',
      type: 'formula',
      inputs: ['total_budget_raw'],
      formula: 'total_budget_raw * 0.05',
    },
  },
  dependencies: {
    budget: [
      'estimated_accounts',
      'estimated_post_engagement',
      'total_budget',
      'budget_per_day',
      'total_budget_raw',
    ],
    audience_location: ['estimated_accounts', 'estimated_post_engagement'],
    audience_age: ['estimated_accounts', 'estimated_post_engagement'],
    audience_gender: ['estimated_accounts', 'estimated_post_engagement'],
    total_budget_raw: ['estimated_vat', 'estimated_vat_raw', 'total_amount'],
    estimated_vat_raw: ['total_amount'],
  },
};

// Helper function to get default values for a specific config type
export const getDefaultValuesForConfig = (type: string): Record<string, any> => {
  const config = getCalculationConfig(type);
  if (!config) return {};

  // Create a temporary engine to get default values
  const tempEngine = new (class TempEngine {
    private config: CalculationConfig;

    constructor(config: CalculationConfig) {
      this.config = config;
    }

    getRequiredInputs(): string[] {
      const inputIds = new Set<string>();
      Object.values(this.config.rules).forEach(rule => {
        rule.inputs.forEach(inputId => inputIds.add(inputId));
      });
      return Array.from(inputIds);
    }

    getDefaultValues(): Record<string, any> {
      const defaults: Record<string, any> = {};
      const requiredInputs = this.getRequiredInputs();

      requiredInputs.forEach(inputId => {
        switch (inputId) {
          case 'budget':
            defaults[inputId] = 1;
            break;
          case 'audience_location':
            defaults[inputId] = ['Vietnam'];
            break;
          case 'audience_age':
            defaults[inputId] = [18, 65];
            break;
          case 'audience_gender':
            defaults[inputId] = 'All';
            break;
          default:
            const rulesUsingInput = Object.values(this.config.rules).filter(rule =>
              rule.inputs.includes(inputId)
            );

            if (rulesUsingInput.length > 0) {
              const firstRule = rulesUsingInput[0];
              if (firstRule.multipliers?.[inputId]) {
                const multiplier = firstRule.multipliers[inputId];
                switch (multiplier.type) {
                  case 'array_length':
                    defaults[inputId] = [];
                    break;
                  case 'range_width':
                    defaults[inputId] = [0, 100];
                    break;
                  case 'linear':
                    defaults[inputId] = 1;
                    break;
                  case 'conditional':
                    defaults[inputId] = '';
                    break;
                  default:
                    defaults[inputId] = 0;
                }
              } else {
                defaults[inputId] = 0;
              }
            } else {
              defaults[inputId] = 0;
            }
        }
      });

      return defaults;
    }
  })(config);

  return tempEngine.getDefaultValues();
};

// Get config. TODO: fetch from Database
export const getCalculationConfig = (type: string): CalculationConfig | null => {
  switch (type) {
    case 'facebook-ad':
      return facebookAdCalculationConfig;
    default:
      return null;
  }
};
