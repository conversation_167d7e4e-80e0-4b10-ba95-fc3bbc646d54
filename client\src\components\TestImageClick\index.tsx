import { useRef, useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { Button, Input } from "../ui";

type Coordinate = {
	x: number;
	y: number;
};

type InteractiveElement = {
	id: string;
	x1: number;
	y1: number;
	x2: number;
	y2: number;
	left: number;
	top: number;
	width: number;
	height: number;
};

export default function TestImageClick() {
	const imageRef = useRef<HTMLImageElement>(null);
	const { imageId } = useParams();

	const [isAddingElement, setIsAddingElement] = useState(false);
	const [coordinates, setCoordinates] = useState<Coordinate[]>([]);
	const [elements, setElements] = useState<InteractiveElement[]>([]);
	const [jsonInput, setJsonInput] = useState("");
	const [exportJson, setExportJson] = useState("");

	// Reset coordinates when exiting adding mode
	useEffect(() => {
		if (!isAddingElement) {
			setCoordinates([]);
		}
	}, [isAddingElement]);
	const handleClick = (event: React.MouseEvent<HTMLImageElement>) => {
		if (!isAddingElement) return;

		const image = imageRef.current;
		if (!image) return;
		const rect = image.getBoundingClientRect();

		// Calculate click position as a percentage of image dimensions
		const clickX = ((event.clientX - rect.left) / rect.width) * 100;
		const clickY = ((event.clientY - rect.top) / rect.height) * 100;

		// Add coordinate
		if (coordinates.length < 4) {
			setCoordinates([...coordinates, { x: clickX, y: clickY }]);
		}
	};

	// Handle double click on elements to delete them
	const handleElementDoubleClick = (id: string, e: React.MouseEvent) => {
		e.stopPropagation(); // Prevent triggering image click
		if (confirm("Delete this element?")) {
			setElements(elements.filter(element => element.id !== id));
		}
	};

	const resetCoordinates = () => {
		setCoordinates([]);
	};
	const confirmElement = () => {
		if (coordinates.length !== 4) {
			alert("Please select 4 points to create an element");
			return;
		}

		// Calculate the bounding box
		let minX = Math.min(...coordinates.map((c) => c.x));
		let minY = Math.min(...coordinates.map((c) => c.y));
		let maxX = Math.max(...coordinates.map((c) => c.x));
		let maxY = Math.max(...coordinates.map((c) => c.y));

		// Calculate left, top, width, height
		const left = minX;
		const top = minY;
		const width = maxX - minX;
		const height = maxY - minY;


		// Log calculations to help debug
		console.log("Points:", coordinates);
		console.log("Calculated bounds:", { minX, minY, maxX, maxY, left, top, width, height });

		// Create new element
		const newElement: InteractiveElement = {
			id: `element-${Date.now()}`,
			x1: coordinates[0].x,
			y1: coordinates[0].y,
			x2: coordinates[2].x,
			y2: coordinates[2].y,
			left,
			top,
			width,
			height,
		};

		// Add to elements
		setElements([...elements, newElement]);

		// Show success message with details
		const elementDetails = `Element added: left=${left.toFixed(2)}%, top=${top.toFixed(2)}%, width=${width.toFixed(2)}%, height=${height.toFixed(2)}%`;
		console.log(elementDetails);

		// Reset state
		setCoordinates([]);
		setIsAddingElement(false);
	};

	const handleExport = () => {
		const json = JSON.stringify(elements, null, 2);
		setExportJson(json);
	};

	const handleImport = () => {
		try {
			const importedElements = JSON.parse(jsonInput);
			if (Array.isArray(importedElements)) {
				setElements(importedElements);
			}
		} catch (error) {
			alert("Invalid JSON format");
		}
	};

	return (
		<div className="p-4">
			<div className="flex flex-col gap-4 w-full">        <div className="flex gap-2 mb-2">
				<Button
					onClick={() => setIsAddingElement(!isAddingElement)}
					variant={isAddingElement ? "secondary" : "default"}
				>
					{isAddingElement ? "Cancel" : "Add New Element"}
				</Button>

				{isAddingElement && (
					<>
						<Button onClick={resetCoordinates} variant="outline">
							Reset Points
						</Button>
						<Button onClick={confirmElement} variant="default">
							Confirm Element
						</Button>
					</>
				)}

				<Button onClick={handleExport} variant="outline">
					Export JSON
				</Button>

				{elements.length > 0 && (
					<div className="ml-auto flex items-center gap-2">
						<span className="text-sm text-gray-600">
							{elements.length} element{elements.length !== 1 ? 's' : ''}
						</span>
						<Button
							onClick={() => {
								if (confirm("Delete all elements?")) {
									setElements([]);
									setExportJson("");
								}
							}}
							variant="destructive"
							size="sm"
						>
							Clear All
						</Button>
					</div>
				)}
			</div>

				{isAddingElement && (
					<div className="mb-4 p-3 border rounded-md bg-gray-50">
						<h3 className="font-medium mb-2">Selection mode: Click 4 points on image</h3>
						<div className="grid grid-cols-4 gap-2">
							{[0, 1, 2, 3].map((index) => (
								<div key={index} className="text-center p-2 border rounded bg-white">
									{coordinates[index] ? (
										<div>
											<div>Point {index + 1}</div>
											<div className="text-sm text-gray-600">
												{coordinates[index].x.toFixed(2)}% x {coordinates[index].y.toFixed(2)}%
											</div>
										</div>
									) : (
										<div className="text-gray-400">Point {index + 1}</div>
									)}
								</div>
							))}
						</div>
					</div>
				)}        <div className="w-full relative">
					{elements.map((element) => (
						<div
							key={element.id}
							className="absolute border-2 border-blue-500 bg-blue-200 bg-opacity-30 hover:bg-opacity-50 transition-all cursor-pointer group"
							style={{
								left: `${element.left}%`,
								top: `${element.top}%`,
								width: `${element.width}%`,
								height: `${element.height}%`,
							}}
							onDoubleClick={(e) => handleElementDoubleClick(element.id, e)}
							title="Double-click to delete"
						>
							<div className="hidden group-hover:block absolute top-0 right-0 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-bl">
								x
							</div>
							<div className="hidden group-hover:flex absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 justify-center">
								{element.width.toFixed(2)}% x {element.height.toFixed(2)}%
							</div>
						</div>
					))}
					<img ref={imageRef} onClick={handleClick} src={`/assets/job-simulation/google-ads/${imageId}`} alt="Test" className="max-w-full h-auto" />
				</div>        <div className="mt-4 grid grid-cols-1 gap-4">
					{elements.length > 0 && (
						<div>
							<h3 className="font-medium mb-2">Element List</h3>
							<div className="border rounded-md overflow-hidden">
								<table className="w-full text-sm">
									<thead className="bg-gray-100">
										<tr>
											<th className="p-2 text-left">ID</th>
											<th className="p-2 text-left">Position</th>
											<th className="p-2 text-left">Size</th>
											<th className="p-2 text-right">Action</th>
										</tr>
									</thead>
									<tbody>
										{elements.map((element) => (
											<tr key={element.id} className="border-t">
												<td className="p-2">{element.id.split('-')[1]}</td>
												<td className="p-2">
													x: {element.left.toFixed(2)}%, y: {element.top.toFixed(2)}%
												</td>
												<td className="p-2">
													{element.width.toFixed(2)}% x {element.height.toFixed(2)}%
												</td>
												<td className="p-2 text-right">
													<button
														className="text-red-500 hover:text-red-700"
														onClick={() => handleElementDoubleClick(element.id, {} as React.MouseEvent)}
													>
														Delete
													</button>
												</td>
											</tr>
										))}
									</tbody>
								</table>
							</div>
						</div>
					)}

					<div>
						<h3 className="font-medium mb-2">Import Elements</h3>
						<div className="flex gap-2">
							<Input
								className="flex-1"
								placeholder="Paste JSON array here"
								value={jsonInput}
								onChange={(e) => setJsonInput(e.target.value)}
							/>
							<Button onClick={handleImport}>Import</Button>
						</div>
					</div>

					{exportJson && (
						<div>
							<h3 className="font-medium mb-2">Exported Elements</h3>
							<pre className="p-3 bg-gray-100 rounded-md overflow-auto max-h-60">{exportJson}</pre>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
