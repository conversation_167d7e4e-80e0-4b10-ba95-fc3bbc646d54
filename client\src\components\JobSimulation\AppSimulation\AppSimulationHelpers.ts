// Helper functions for App Simulation components
// This file contains utility functions to support generalized app simulation

import { CalculationEngine } from './CalculationEngine';
import { getCalculationConfig, getDefaultValuesForConfig } from './CalculationConfigs';

/**
 * Initialize default dynamic selections for a given app simulation type
 * @param simulationType - The type of simulation (e.g., 'facebook-ad')
 * @returns Default values object for dynamic selections
 */
export const initializeDefaultSelections = (simulationType: string): Record<string, any> => {
  try {
    const defaultValues = getDefaultValuesForConfig(simulationType);
    console.log(`Initializing default selections for ${simulationType}:`, defaultValues);
    return defaultValues;
  } catch (error) {
    console.error(`Error initializing default selections for ${simulationType}:`, error);
    // Return empty object as fallback
    return {};
  }
};

/**
 * Create and configure a calculation engine for a given simulation type
 * @param simulationType - The type of simulation (e.g., 'facebook-ad')
 * @returns Configured CalculationEngine instance
 */
export const createCalculationEngine = (simulationType: string): CalculationEngine | null => {
  try {
    const config = getCalculationConfig(simulationType);
    if (!config) {
      console.warn(`No calculation config found for simulation type: ${simulationType}`);
      return null;
    }
    
    return new CalculationEngine(config);
  } catch (error) {
    console.error(`Error creating calculation engine for ${simulationType}:`, error);
    return null;
  }
};

/**
 * Safely update calculation engine with new values
 * @param engine - The calculation engine instance
 * @param values - New values to update
 * @returns Calculated estimates or empty object on error
 */
export const safeCalculateEstimates = (
  engine: CalculationEngine | null, 
  values: Record<string, any>
): Record<string, any> => {
  if (!engine) {
    console.warn('No calculation engine available');
    return {};
  }

  try {
    // Check if we have at least some required inputs
    const requiredInputs = engine.getRequiredInputs();
    const hasAnyRequiredInput = requiredInputs.some(inputId => 
      values.hasOwnProperty(inputId)
    );
    
    if (Object.keys(values).length === 0 && !hasAnyRequiredInput) {
      console.log('Skipping calculation: no values available');
      return {};
    }

    engine.updateValues(values);
    const estimates = engine.getAllValues();
    
    console.log('Calculation Engine - Updated values:', values);
    console.log('Calculation Engine - Results:', estimates);
    
    return estimates;
  } catch (error) {
    console.error('Error in calculation engine:', error);
    return {};
  }
};

/**
 * Validate if dynamic selections have minimum required data
 * @param selections - Current dynamic selections
 * @param engine - Calculation engine instance
 * @returns true if selections are valid for calculation
 */
export const validateDynamicSelections = (
  selections: Record<string, any>,
  engine: CalculationEngine | null
): boolean => {
  if (!engine || !selections) return false;
  
  const requiredInputs = engine.getRequiredInputs();
  
  // Check if we have at least one required input
  return requiredInputs.some(inputId => selections.hasOwnProperty(inputId));
};

/**
 * Get missing required inputs for debugging
 * @param selections - Current dynamic selections
 * @param engine - Calculation engine instance
 * @returns Array of missing input IDs
 */
export const getMissingRequiredInputs = (
  selections: Record<string, any>,
  engine: CalculationEngine | null
): string[] => {
  if (!engine) return [];
  
  const requiredInputs = engine.getRequiredInputs();
  return requiredInputs.filter(inputId => !selections.hasOwnProperty(inputId));
};
